'use client';

import GlobalTrackingFilter from '@/components/tracking/filter/global-tracking-filter';
import GlobalFilterProvider from '@/components/tracking/filter/global-filter-context';
import ClocTrackingHeatmap from '@/components/tracking/tracking-heatmap';
import ClocTrackingSessionReplay from '@/components/tracking/tracking-session-replay';
import TrackingAnalytics from '@/components/tracking/tracking-analytics';

export default function ReplayPage() {
	return (
		<div className="w-full h-full">
			<GlobalFilterProvider>
				<GlobalTrackingFilter />
				<TrackingAnalytics showIndividualComponents={true} />
				<ClocTrackingSessionReplay className="mb-4" />
				<ClocTrackingHeatmap />
			</GlobalFilterProvider>
		</div>
	);
}
