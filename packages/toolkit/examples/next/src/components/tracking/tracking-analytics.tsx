'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { Data, decode } from 'clarity-decode';
import { cn, Button } from '@cloc/ui';
import { BarChart3, Loader2, RefreshCw, Download } from 'lucide-react';
import { useGlobalFilter } from './filter/global-filter-context';
import AnalyticsDashboard from './insight/analytics-dashboard';
import SessionAnalyticsDashboard from './insight/session-analytics-dashboard';
import ClickAnalyticsComponent from './insight/click-analytics';

interface TrackingAnalyticsProps {
	className?: string;
	showIndividualComponents?: boolean;
	autoRefresh?: boolean;
	refreshInterval?: number; // in milliseconds
}

type AnalyticsView = 'dashboard' | 'session' | 'clicks';

const TrackingAnalytics: React.FC<TrackingAnalyticsProps> = ({
	className,
	showIndividualComponents = false,
	autoRefresh = false,
	refreshInterval = 30000 // 30 seconds
}) => {
	const { sessions, loading, error, fetchSessions, refetchSessions } = useGlobalFilter();

	const [currentView, setCurrentView] = useState<AnalyticsView>('dashboard');
	const [isRefreshing, setIsRefreshing] = useState(false);

	// Decode all session payloads for analytics
	const allDecodedPayloads = useMemo(() => {
		if (!sessions?.length) return [];

		const decodedPayloads: Data.DecodedPayload[] = [];

		sessions.forEach((session) => {
			try {
				const sessionPayloads = session.payloads.map((payload) => decode(payload));
				decodedPayloads.push(...sessionPayloads);
			} catch (error) {
				console.warn('Failed to decode session payloads:', error);
			}
		});

		return decodedPayloads;
	}, [sessions]);

	// Use all decoded payloads for analytics
	const activePayloads = allDecodedPayloads;

	// Auto-refresh functionality
	useEffect(() => {
		if (!autoRefresh) return;

		const interval = setInterval(async () => {
			setIsRefreshing(true);
			try {
				await refetchSessions();
			} catch (error) {
				console.error('Auto-refresh failed:', error);
			} finally {
				setIsRefreshing(false);
			}
		}, refreshInterval);

		return () => clearInterval(interval);
	}, [autoRefresh, refreshInterval, refetchSessions]);

	// Manual refresh handler
	const handleManualRefresh = async () => {
		setIsRefreshing(true);
		try {
			await refetchSessions();
		} catch (error) {
			console.error('Manual refresh failed:', error);
		} finally {
			setIsRefreshing(false);
		}
	};

	// Export analytics data (placeholder for future implementation)
	const handleExportData = () => {
		// TODO: Implement data export functionality
		console.log('Export analytics data:', {
			sessions: sessions?.length || 0,
			payloads: activePayloads.length,
			timestamp: new Date().toISOString()
		});
	};

	// View toggle buttons
	const viewButtons = [
		{ key: 'dashboard' as AnalyticsView, label: 'Dashboard', icon: BarChart3 },
		{ key: 'session' as AnalyticsView, label: 'Session', icon: BarChart3 },
		{ key: 'clicks' as AnalyticsView, label: 'Clicks', icon: BarChart3 }
	];

	if (error) {
		return (
			<div
				className={cn(
					'bg-white dark:bg-gray-800 rounded-lg border border-red-200 dark:border-red-800 p-8',
					className
				)}
			>
				<div className="text-center text-red-600 dark:text-red-400">
					<BarChart3 size={48} className="mx-auto mb-4 opacity-50" />
					<h3 className="text-lg font-medium mb-2">Analytics Error</h3>
					<p className="text-sm mb-4">{error}</p>
					<Button onClick={handleManualRefresh} variant="outline" size="sm" disabled={isRefreshing}>
						{isRefreshing ? <Loader2 size={16} className="animate-spin" /> : <RefreshCw size={16} />}
						Retry
					</Button>
				</div>
			</div>
		);
	}

	if (loading && !sessions?.length) {
		return (
			<div
				className={cn(
					'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8',
					className
				)}
			>
				<div className="flex items-center justify-center gap-2 text-gray-500 dark:text-gray-400">
					<Loader2 size={20} className="animate-spin" />
					<span>Loading analytics data...</span>
				</div>
			</div>
		);
	}

	if (!sessions?.length) {
		return (
			<div
				className={cn(
					'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8',
					className
				)}
			>
				<div className="text-center text-gray-500 dark:text-gray-400">
					<BarChart3 size={48} className="mx-auto mb-4 opacity-50" />
					<h3 className="text-lg font-medium mb-2">No Analytics Data</h3>
					<p className="text-sm">No session data available</p>
				</div>
			</div>
		);
	}

	return (
		<div className={cn('space-y-6', className)}>
			{/* Header with Controls */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
						<BarChart3 size={28} className="text-blue-600 dark:text-blue-400" />
						Tracking Analytics
					</h1>
					<p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
						Real-time insights from user session data
					</p>
				</div>

				<div className="flex items-center gap-2">
					{/* Auto-refresh indicator */}
					{autoRefresh && (
						<div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded">
							<div
								className={cn(
									'w-2 h-2 rounded-full',
									isRefreshing ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'
								)}
							/>
							Auto-refresh
						</div>
					)}

					{/* Manual refresh button */}
					<Button onClick={handleManualRefresh} variant="outline" size="sm" disabled={isRefreshing}>
						{isRefreshing ? <Loader2 size={16} className="animate-spin" /> : <RefreshCw size={16} />}
						Refresh
					</Button>

					{/* Export button */}
					<Button onClick={handleExportData} variant="outline" size="sm">
						<Download size={16} />
						Export
					</Button>
				</div>
			</div>

			{/* View Toggle (only show if individual components are enabled) */}
			{showIndividualComponents && (
				<div className="flex items-center gap-2 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
					{viewButtons.map(({ key, label, icon: Icon }) => (
						<button
							key={key}
							onClick={() => setCurrentView(key)}
							className={cn(
								'flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors',
								currentView === key
									? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
									: 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
							)}
						>
							<Icon size={16} />
							{label}
						</button>
					))}
				</div>
			)}

			{/* Analytics Content */}
			{showIndividualComponents ? (
				<div>
					{currentView === 'dashboard' && (
						<AnalyticsDashboard decodedPayloads={activePayloads} defaultExpanded={true} />
					)}
					{currentView === 'session' && <SessionAnalyticsDashboard decodedPayloads={activePayloads} />}
					{currentView === 'clicks' && (
						<ClickAnalyticsComponent decodedPayloads={activePayloads} showElementDetails={true} />
					)}
				</div>
			) : (
				<AnalyticsDashboard decodedPayloads={activePayloads} defaultExpanded={true} />
			)}

			{/* Data Summary Footer */}
			{/* <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
				<div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
					<div className="flex items-center gap-4">
						<span>
							{sessions.length} session{sessions.length !== 1 ? 's' : ''} loaded
						</span>
						<span>
							{activePayloads.length} payload{activePayloads.length !== 1 ? 's' : ''} analyzed
						</span>
					</div>
					<div>Last updated: {new Date().toLocaleTimeString()}</div>
				</div>
			</div> */}
		</div>
	);
};

export default TrackingAnalytics;
export { TrackingAnalytics };
export type { TrackingAnalyticsProps };
